import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { User } from 'src/schemas/user.schema';
export declare class AdminDebugController {
    private readonly jwtService;
    private userModel;
    constructor(jwtService: JwtService, userModel: Model<User>);
    getTokenInfo(req: any): Promise<{
        success: boolean;
        payload: any;
        userID: any;
        hasRole: boolean;
        role: any;
        isAdmin: boolean;
        error?: undefined;
        token?: undefined;
    } | {
        success: boolean;
        error: any;
        token: string;
        payload?: undefined;
        userID?: undefined;
        hasRole?: undefined;
        role?: undefined;
        isAdmin?: undefined;
    }>;
    testAdminAccess(): Promise<{
        success: boolean;
        message: string;
        timestamp: string;
    }>;
    testNoAuth(): Promise<{
        success: boolean;
        message: string;
        timestamp: string;
    }>;
    getUsersInfo(): Promise<{
        success: boolean;
        totalUsers: number;
        adminUsers: number;
        studentUsers: number;
        users: {
            id: import("mongoose").Types.ObjectId;
            email: string;
            role: string;
        }[];
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        totalUsers?: undefined;
        adminUsers?: undefined;
        studentUsers?: undefined;
        users?: undefined;
    }>;
}
