import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { AdminDebugController } from './admin-debug.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Subject, SubjectSchema } from 'src/schemas/subject.schema';
import { User, UserSchema } from 'src/schemas/user.schema';
import { JwtModule } from '@nestjs/jwt';
import jwtConfig from 'src/config/jwtConfig';
import { Quiz, QuizSchema } from 'src/schemas/quiz.schema';

@Module({
  imports: [
    JwtModule.registerAsync(jwtConfig.asProvider()),
    MongooseModule.forFeature([
      { name: Subject.name, schema: SubjectSchema },
      { name: Quiz.name, schema: QuizSchema },
      { name: User.name, schema: UserSchema }
    ])
  ],
  controllers: [AdminController, AdminDebugController],
  providers: [AdminService],
  exports: [AdminService]
})
export class AdminModule {}
