"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-text";
exports.ids = ["vendor-chunks/hast-util-to-text"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-text/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-text/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toText: () => (/* binding */ toText)\n/* harmony export */ });\n/* harmony import */ var unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-find-after */ \"(ssr)/./node_modules/unist-util-find-after/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast-util-is-element').TestFunction} TestFunction\n */\n\n/**\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n *\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n *\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n *\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n *\n * @typedef {BreakNumber | BreakValue | undefined} BreakBefore\n *   Any value for a break before.\n *\n * @typedef {BreakForce | BreakNumber | BreakValue | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use (default: `'normal'`).\n */\n\n\n\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('br')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(isCell)\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst row = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  // List from: <https://html.spec.whatwg.org/multipage/rendering.html#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/multipage/rendering.html#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/multipage/rendering.html#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'li', // Lists (as `display: list-item`)\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Nodes} tree\n *   Tree to turn into text.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nfunction toText(tree, options) {\n  const options_ = options || {}\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options_.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<BreakNumber | string>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      ...renderedTextCollection(\n        children[index],\n        // @ts-expect-error: `tree` is a parent if we’re here.\n        tree,\n        {\n          whitespace,\n          breakBefore: index ? undefined : block,\n          breakAfter:\n            index < children.length - 1 ? br(children[index + 1]) : block\n        }\n      )\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/multipage/dom.html#rendered-text-collection-steps>\n *\n * @param {Nodes} node\n * @param {Parents} parent\n * @param {CollectionInfo} info\n * @returns {Array<BreakNumber | string>}\n */\nfunction renderedTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parents} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<BreakNumber | string>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakForce | BreakNumber | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (\n    row(node) &&\n    // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, row)\n  ) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      renderedTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  if (\n    cell(node) &&\n    // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, cell)\n  ) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Comment | Text} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<BreakNumber | string>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x20_0b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x20_0b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Nodes} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const properties = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return properties.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {properties: {hidden: true}}}\n */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {tagName: 'td' | 'th'}}\n */\nfunction isCell(node) {\n  return node.tagName === 'td' || node.tagName === 'th'\n}\n\n/**\n * @type {TestFunction}\n */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-text/lib/index.js\n");

/***/ })

};
;