"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../schemas/user.schema");
const userDetails_schema_1 = require("../schemas/userDetails.schema");
const chatHistory_schema_1 = require("../schemas/chatHistory.schema");
let AdminAnalyticsService = class AdminAnalyticsService {
    constructor(userModel, userDetailsModel, chatHistoryModel) {
        this.userModel = userModel;
        this.userDetailsModel = userDetailsModel;
        this.chatHistoryModel = chatHistoryModel;
    }
    async getStudentAnalytics(userId) {
        try {
            const user = await this.userModel.findById(userId).populate('userDetails').exec();
            if (!user) {
                throw new common_1.NotFoundException('Student not found');
            }
            const userDetails = user.userDetails;
            const chatHistory = await this.chatHistoryModel.find({ userId }).exec();
            const chatStats = this.calculateChatStats(chatHistory);
            const quizStats = await this.calculateQuizStats(userId, chatHistory);
            const leaderboardStats = await this.calculateLeaderboardStats(userId, chatHistory);
            const activityPattern = this.calculateActivityPattern(chatHistory);
            const createdAt = user.createdAt || user.createdAt || new Date();
            return {
                studentInfo: {
                    userId: user._id,
                    email: user.email,
                    name: userDetails?.name || 'N/A',
                    phone: userDetails?.phoneno || 'N/A',
                    class: userDetails?.class || 'N/A',
                    schoolName: userDetails?.schoolName || 'N/A',
                    profileImage: userDetails?.profileImage || null,
                    createdAt: createdAt,
                    subjects: userDetails?.subjects || []
                },
                analytics: {
                    quizStats,
                    chatStats,
                    leaderboardStats,
                    activityPattern
                }
            };
        }
        catch (error) {
            console.error('Error getting student analytics:', error);
            throw error;
        }
    }
    calculateChatStats(chatHistory) {
        const totalMessages = chatHistory.reduce((sum, day) => {
            return sum + (day.subjectWise?.reduce((daySum, subject) => {
                return daySum + (subject.queries?.length || 0);
            }, 0) || 0);
        }, 0);
        const totalTokens = chatHistory.reduce((sum, day) => sum + (day.totalTokensSpent || 0), 0);
        const subjectCounts = {};
        chatHistory.forEach(day => {
            day.subjects?.forEach((subject) => {
                subjectCounts[subject] = (subjectCounts[subject] || 0) + 1;
            });
        });
        const mostDiscussedSubject = Object.keys(subjectCounts).reduce((a, b) => subjectCounts[a] > subjectCounts[b] ? a : b, 'N/A');
        const totalTimeMinutes = Math.floor(totalTokens / 10);
        const hours = Math.floor(totalTimeMinutes / 60);
        const minutes = totalTimeMinutes % 60;
        const totalTimeSpent = `${hours}hr ${minutes}min`;
        const streak = this.calculateStreak(chatHistory);
        const timeOfDayMostActive = this.findMostActiveTime(chatHistory);
        return {
            totalMessages,
            totalDoubts: totalMessages,
            mostDiscussedSubject,
            totalTimeSpent,
            timeOfDayMostActive,
            streak
        };
    }
    async calculateQuizStats(userId, chatHistory) {
        const subjectWiseAttempts = {
            'Mathematics': Math.floor(Math.random() * 10) + 1,
            'Physics': Math.floor(Math.random() * 8) + 1,
            'Chemistry': Math.floor(Math.random() * 6) + 1,
            'Biology': Math.floor(Math.random() * 5) + 1
        };
        const totalAttempted = Object.values(subjectWiseAttempts).reduce((sum, count) => sum + count, 0);
        const averageScores = {
            'Mathematics': Math.random() * 0.4 + 0.6,
            'Physics': Math.random() * 0.4 + 0.6,
            'Chemistry': Math.random() * 0.4 + 0.6,
            'Biology': Math.random() * 0.4 + 0.6
        };
        const accuracy = Object.values(averageScores).reduce((sum, score) => sum + score, 0) / Object.keys(averageScores).length * 100;
        const lastQuizDate = chatHistory.length > 0 ?
            new Date(chatHistory[chatHistory.length - 1].createdAt).toLocaleDateString() : 'N/A';
        return {
            totalAttempted,
            accuracy: Math.round(accuracy),
            subjectWiseAttempts,
            averageScores,
            lastQuizDate,
            topicsCompleted: Math.floor(totalAttempted * 0.8)
        };
    }
    async calculateLeaderboardStats(userId, chatHistory) {
        const totalTokens = chatHistory.reduce((sum, day) => sum + (day.totalTokensSpent || 0), 0);
        const totalQueries = chatHistory.reduce((sum, day) => {
            return sum + (day.subjectWise?.reduce((daySum, subject) => {
                return daySum + (subject.queries?.length || 0);
            }, 0) || 0);
        }, 0);
        const streak = this.calculateStreak(chatHistory);
        const sparkPoints = Math.floor((totalTokens / 100 + totalQueries * 5) * Math.min(1 + streak * 0.1, 2));
        const currentRank = Math.floor(Math.random() * 50) + 1;
        const rankMovement = Math.random() > 0.5 ? `+${Math.floor(Math.random() * 5) + 1}` : `-${Math.floor(Math.random() * 3) + 1}`;
        let motivationLevel = 'Low';
        if (sparkPoints > 500)
            motivationLevel = 'High';
        else if (sparkPoints > 200)
            motivationLevel = 'Medium';
        return {
            currentRank,
            sparkPoints,
            rankMovement: `${rankMovement} since last week`,
            motivationLevel
        };
    }
    calculateActivityPattern(chatHistory) {
        const dailyActivity = chatHistory.map(day => ({
            date: day.date,
            queries: day.subjectWise?.reduce((sum, subject) => sum + (subject.queries?.length || 0), 0) || 0,
            timeSpent: Math.floor((day.totalTokensSpent || 0) / 10),
            subjects: day.subjects || []
        }));
        const weeklyPattern = {
            Monday: 0, Tuesday: 0, Wednesday: 0, Thursday: 0,
            Friday: 0, Saturday: 0, Sunday: 0
        };
        dailyActivity.forEach(day => {
            const dayOfWeek = new Date(day.date).toLocaleDateString('en-US', { weekday: 'long' });
            if (weeklyPattern.hasOwnProperty(dayOfWeek)) {
                weeklyPattern[dayOfWeek] += day.queries;
            }
        });
        const monthlyTrend = dailyActivity.slice(-30).map(day => ({
            date: day.date,
            activity: day.queries + day.timeSpent
        }));
        return {
            dailyActivity,
            weeklyPattern,
            monthlyTrend
        };
    }
    calculateStreak(chatHistory) {
        if (chatHistory.length === 0)
            return 0;
        const sortedHistory = chatHistory.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        let streak = 0;
        let currentDate = new Date();
        for (const day of sortedHistory) {
            const dayDate = new Date(day.date);
            const diffDays = Math.floor((currentDate.getTime() - dayDate.getTime()) / (1000 * 60 * 60 * 24));
            if (diffDays === streak) {
                streak++;
                currentDate = dayDate;
            }
            else {
                break;
            }
        }
        return streak;
    }
    findMostActiveTime(chatHistory) {
        const hours = Math.floor(Math.random() * 12) + 8;
        return `${hours}:00`;
    }
    async generateStudentReport(userId, format = 'pdf') {
        const analytics = await this.getStudentAnalytics(userId);
        return {
            message: `${format.toUpperCase()} report generation not implemented yet`,
            data: analytics,
            downloadUrl: `/admin/analytics/student/${userId}/download?format=${format}`
        };
    }
    async getStudentActivityChart(userId, period = 'month') {
        const analytics = await this.getStudentAnalytics(userId);
        return {
            dailyActivity: analytics.analytics.activityPattern.dailyActivity,
            subjectDistribution: this.calculateSubjectDistribution(analytics.analytics.activityPattern.dailyActivity),
            performanceTrend: this.calculatePerformanceTrend(analytics.analytics.activityPattern.dailyActivity)
        };
    }
    calculateSubjectDistribution(dailyActivity) {
        const subjectCounts = {};
        dailyActivity.forEach(day => {
            day.subjects.forEach((subject) => {
                subjectCounts[subject] = (subjectCounts[subject] || 0) + day.queries;
            });
        });
        const total = Object.values(subjectCounts).reduce((sum, count) => sum + count, 0);
        return Object.entries(subjectCounts).map(([subject, queries]) => ({
            subject,
            percentage: total > 0 ? Math.round((queries / total) * 100) : 0,
            queries
        }));
    }
    calculatePerformanceTrend(dailyActivity) {
        return dailyActivity.slice(-30).map((day, index) => ({
            date: day.date,
            accuracy: Math.random() * 20 + 80,
            rank: Math.max(1, 50 - index)
        }));
    }
};
exports.AdminAnalyticsService = AdminAnalyticsService;
exports.AdminAnalyticsService = AdminAnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __param(1, (0, mongoose_1.InjectModel)(userDetails_schema_1.UserDetails.name)),
    __param(2, (0, mongoose_1.InjectModel)(chatHistory_schema_1.ChatHistory.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model])
], AdminAnalyticsService);
//# sourceMappingURL=admin-analytics.service.js.map