"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../schemas/user.schema");
const jwt_1 = require("@nestjs/jwt");
const encrypt_decrypt_1 = require("../utils/encrypt_decrypt");
let AuthService = class AuthService {
    constructor(userModel, jwtService) {
        this.userModel = userModel;
        this.jwtService = jwtService;
    }
    async login(loginDto) {
        const { email, password } = loginDto;
        console.log('Login attempt for email:', email);
        const user = await this.userModel.findOne({ email });
        if (!user) {
            console.log('User not found for email:', email);
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        console.log('User found:', { email: user.email, role: user.role });
        console.log('Encrypted password from DB:', user.password);
        const decryptedPassword = (0, encrypt_decrypt_1.decryptData)(user.password).toString();
        console.log('Decrypted password:', decryptedPassword);
        console.log('Provided password:', password);
        console.log('Password match:', decryptedPassword === password);
        const isPasswordValid = (decryptedPassword === password);
        if (!isPasswordValid) {
            console.log('Password validation failed');
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        console.log('Login successful for user:', email);
        const payload = { email: user.email, sub: user._id, role: user.role };
        const token = await this.jwtService.signAsync(payload);
        if (user.userDetails != null) {
            return { accessToken: token, isUserDetailsPresent: true };
        }
        return { accessToken: token, isUserDetailsPresent: false };
    }
    async register(registerDto) {
        const { email, password } = registerDto;
        const existingUser = await this.userModel.findOne({ email });
        if (existingUser) {
            throw new common_1.ConflictException('User already exists');
        }
        const encryptedMessage = (0, encrypt_decrypt_1.encryptData)(password);
        const newUser = new this.userModel({ email, password: encryptedMessage, role: 'student' });
        if (await newUser.save()) {
            return { success: true };
        }
        else {
            return { success: false };
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map