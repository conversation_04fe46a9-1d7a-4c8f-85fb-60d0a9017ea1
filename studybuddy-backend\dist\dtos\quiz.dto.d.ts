export declare class OptionDto {
    text: string;
    isCorrect: boolean;
}
export declare class CreateQuizDto {
    question: string;
    options: OptionDto[];
    subjectId: string;
    topicId: string;
    type?: string;
    difficulty?: number;
    explanation?: string;
}
export declare class UpdateQuizDto {
    question?: string;
    options?: OptionDto[];
    type?: string;
    difficulty?: number;
    explanation?: string;
}
export declare class QuizFilterDto {
    subjectId?: string;
    topicId?: string;
    noOfQuestions?: number;
}
