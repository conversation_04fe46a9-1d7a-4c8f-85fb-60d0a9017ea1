"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const subject_schema_1 = require("../schemas/subject.schema");
const quiz_schema_1 = require("../schemas/quiz.schema");
let AdminService = class AdminService {
    constructor(subjectModel, quizModel) {
        this.subjectModel = subjectModel;
        this.quizModel = quizModel;
    }
    async getAllSubjects() {
        return this.subjectModel.find().exec();
    }
    async getSubjectById(id) {
        const subject = await this.subjectModel.findById(id).exec();
        if (!subject) {
            throw new common_1.NotFoundException(`Subject with ID ${id} not found`);
        }
        return subject;
    }
    async createSubject(subjectDto) {
        const existingSubject = await this.subjectModel.findOne({ name: subjectDto.name }).exec();
        if (existingSubject) {
            throw new common_1.ConflictException(`Subject with name ${subjectDto.name} already exists`);
        }
        const newSubject = new this.subjectModel(subjectDto);
        return newSubject.save();
    }
    async updateSubject(id, updateSubjectDto) {
        const updatedSubject = await this.subjectModel.findByIdAndUpdate(id, updateSubjectDto, { new: true }).exec();
        if (!updatedSubject) {
            throw new common_1.NotFoundException(`Subject with ID ${id} not found`);
        }
        return updatedSubject;
    }
    async deleteSubject(id) {
        const result = await this.subjectModel.findByIdAndDelete(id).exec();
        if (!result) {
            throw new common_1.NotFoundException(`Subject with ID ${id} not found`);
        }
        return { success: true };
    }
    async addTopic(addTopicDto) {
        const subject = await this.subjectModel.findById(addTopicDto.subjectId).exec();
        if (!subject) {
            throw new common_1.NotFoundException(`Subject with ID ${addTopicDto.subjectId} not found`);
        }
        subject.topics.push(addTopicDto.topic);
        return subject.save();
    }
    async updateTopic(updateTopicDto) {
        const subject = await this.subjectModel.findById(updateTopicDto.subjectId).exec();
        if (!subject) {
            throw new common_1.NotFoundException(`Subject with ID ${updateTopicDto.subjectId} not found`);
        }
        const topicIndex = subject.topics.findIndex(topic => topic._id.toString() === updateTopicDto.topicId);
        if (topicIndex === -1) {
            throw new common_1.NotFoundException(`Topic with ID ${updateTopicDto.topicId} not found`);
        }
        const updatedTopic = {
            ...subject.topics[topicIndex].toObject(),
            name: updateTopicDto.topic.name,
            description: updateTopicDto.topic.description
        };
        subject.topics[topicIndex] = updatedTopic;
        return subject.save();
    }
    async deleteTopic(subjectId, topicId) {
        const subject = await this.subjectModel.findById(subjectId).exec();
        if (!subject) {
            throw new common_1.NotFoundException(`Subject with ID ${subjectId} not found`);
        }
        const topicIndex = subject.topics.findIndex(topic => topic._id.toString() === topicId);
        if (topicIndex === -1) {
            throw new common_1.NotFoundException(`Topic with ID ${topicId} not found`);
        }
        subject.topics.splice(topicIndex, 1);
        return subject.save();
    }
    async createQuiz(createQuizDto) {
        const subject = await this.subjectModel.findById(createQuizDto.subjectId).exec();
        if (!subject) {
            throw new common_1.NotFoundException(`Subject with ID ${createQuizDto.subjectId} not found`);
        }
        const topicExists = subject.topics.some(topic => topic._id.toString() === createQuizDto.topicId);
        if (!topicExists) {
            throw new common_1.NotFoundException(`Topic with ID ${createQuizDto.topicId} not found in subject`);
        }
        const hasCorrectAnswer = createQuizDto.options.some(option => option.isCorrect);
        if (!hasCorrectAnswer) {
            throw new common_1.BadRequestException('Quiz must have at least one correct answer');
        }
        const newQuiz = new this.quizModel(createQuizDto);
        return newQuiz.save();
    }
    async getAllQuizzes(filterDto) {
        const filter = {};
        if (filterDto.subjectId) {
            filter.subjectId = filterDto.subjectId;
        }
        if (filterDto.topicId) {
            filter.topicId = filterDto.topicId;
        }
        let query = this.quizModel.find(filter);
        console.log('noOfQuestions:', filterDto.noOfQuestions, typeof filterDto.noOfQuestions);
        if (filterDto.noOfQuestions && filterDto.noOfQuestions > 0) {
            query = query.limit(Number(filterDto.noOfQuestions));
        }
        return query.exec();
    }
    async getQuizById(id) {
        const quiz = await this.quizModel.findById(id).exec();
        if (!quiz) {
            throw new common_1.NotFoundException(`Quiz with ID ${id} not found`);
        }
        return quiz;
    }
    async updateQuiz(id, updateQuizDto) {
        if (updateQuizDto.options) {
            const hasCorrectAnswer = updateQuizDto.options.some(option => option.isCorrect);
            if (!hasCorrectAnswer) {
                throw new common_1.BadRequestException('Quiz must have at least one correct answer');
            }
        }
        const updatedQuiz = await this.quizModel.findByIdAndUpdate(id, updateQuizDto, { new: true }).exec();
        if (!updatedQuiz) {
            throw new common_1.NotFoundException(`Quiz with ID ${id} not found`);
        }
        return updatedQuiz;
    }
    async deleteQuiz(id) {
        const result = await this.quizModel.findByIdAndDelete(id).exec();
        if (!result) {
            throw new common_1.NotFoundException(`Quiz with ID ${id} not found`);
        }
        return { success: true };
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(subject_schema_1.Subject.name)),
    __param(1, (0, mongoose_1.InjectModel)(quiz_schema_1.Quiz.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], AdminService);
//# sourceMappingURL=admin.service.js.map