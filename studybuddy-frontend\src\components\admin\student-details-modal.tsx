"use client"

import { X, Download, User, Award, MessageCircle, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { useState, useEffect } from "react"
import AnalyticsCharts from "./analytics-charts"

interface Student {
  _id: string
  email: string
  password: string
  name?: string
  phone?: string
  class?: string
  schoolName?: string
  place?: string
  createdOn?: string
  createdBy?: string
  avatar?: string
}

interface StudentInfo {
  userId: string
  email: string
  name: string
  phone: string
  class: string
  schoolName: string
  profileImage?: string
  createdAt: string
  subjects: string[]
}

interface ChartData {
  dailyActivity: Array<{
    date: string
    queries: number
    timeSpent: number
    subjects: string[]
  }>
  subjectDistribution: Array<{
    subject: string
    percentage: number
    queries: number
  }>
  performanceTrend: Array<{
    date: string
    accuracy: number
    rank: number
  }>
}

interface StudentAnalytics {
  quizzesAttempted: number
  quizAccuracy: number
  totalTimeSpent: string
  currentLearningStreak: number
  motivationLevel: string
  currentRank: number
  rankMovement: string
  flamesEarned: number
  topicsCompleted: number
  averageScores: {
    maths: number
    physics: number
    chemistry: number
    biology: number
  }
  lastQuizDate: string
  timeOfDayMostActive: string
  chatStats: {
    totalMessagesSent: number
    totalDoubtsAsked: number
    mostDiscussedSubject: string
  }
}

interface StudentDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  student: Student | null
}

export default function StudentDetailsModal({ isOpen, onClose, student }: StudentDetailsModalProps) {
  const { toast } = useToast()
  const [analytics, setAnalytics] = useState<StudentAnalytics | null>(null)
  const [chartData, setChartData] = useState<ChartData | null>(null)
  const [studentInfo, setStudentInfo] = useState<StudentInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)

  // Fetch student analytics when modal opens
  useEffect(() => {
    if (isOpen && student?._id) {
      fetchStudentAnalytics()
    }
  }, [isOpen, student?._id])

  const getAuthHeaders = () => {
    const token = localStorage.getItem('accessToken')
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }

  const fetchStudentAnalytics = async () => {
    if (!student?._id) return

    setIsLoading(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/admin/analytics/student/${student._id}`, {
        headers: getAuthHeaders()
      })

      if (!response.ok) {
        throw new Error('Failed to fetch student analytics')
      }

      const data = await response.json()

      // Set student info
      setStudentInfo(data.studentInfo)

      // Set analytics data
      setAnalytics(data.analytics)

      // Fetch chart data
      const chartResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/admin/analytics/student/${student._id}/activity-chart`, {
        headers: getAuthHeaders()
      })

      if (chartResponse.ok) {
        const chartData = await chartResponse.json()
        setChartData(chartData)
      }

    } catch (error) {
      console.error('Error fetching student analytics:', error)
      toast({
        title: "Error",
        description: "Failed to load student analytics. Please try again.",
      })

      // Fallback to mock data
      setAnalytics({
        quizzesAttempted: 23,
        quizAccuracy: 98,
        totalTimeSpent: "23hr 40 Mins",
        currentLearningStreak: 26,
        motivationLevel: "High",
        currentRank: 1,
        rankMovement: "+2 since last week",
        flamesEarned: 26,
        topicsCompleted: 23,
        averageScores: {
          maths: 0.8,
          physics: 0.9,
          chemistry: 0.7,
          biology: 0.6,
        },
        lastQuizDate: "24 May (Maths - 18/20)",
        timeOfDayMostActive: "2:00 PM",
        chatStats: {
          totalMessagesSent: 123,
          totalDoubtsAsked: 26,
          mostDiscussedSubject: "Physics",
        },
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = async () => {
    if (!student?._id) return

    setIsDownloading(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/admin/analytics/student/${student._id}/download?format=pdf`, {
        headers: getAuthHeaders()
      })

      if (!response.ok) {
        throw new Error('Failed to generate report')
      }

      const data = await response.json()

      toast({
        title: "Download Ready",
        description: "Student report has been generated successfully.",
      })

      // In a real implementation, you would handle the actual file download here
      console.log('Download data:', data)

    } catch (error) {
      console.error('Error downloading report:', error)
      toast({
        title: "Error",
        description: "Failed to generate report. Please try again.",
      })
    } finally {
      setIsDownloading(false)
    }
  }

  if (!isOpen || !student) return null

  const displayStudentInfo = studentInfo || {
    name: student.name || "N/A",
    phone: student.phone || "N/A",
    class: student.class || "N/A",
    schoolName: student.schoolName || "N/A",
    email: student.email,
    createdAt: student.createdOn || new Date().toISOString(),
    subjects: []
  }



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 max-h-[95vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <User className="w-6 h-6 text-primary-blue" />
            <h2 className="text-xl font-semibold text-gray-900">Student Details</h2>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              onClick={handleDownload}
              disabled={isDownloading}
              className="bg-primary-blue hover:bg-primary-blue/90 text-white px-4 py-2 text-sm flex items-center"
            >
              {isDownloading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              {isDownloading ? 'Generating...' : 'Download Details'}
            </Button>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-primary-blue" />
              <span className="ml-2 text-gray-600">Loading student analytics...</span>
            </div>
          ) : (
            <>
          {/* Student Profile Section */}
          <div className="bg-primary-dark rounded-lg p-6 text-white">
            <div className="flex items-center space-x-6">
              <div className="w-32 h-32 bg-primary-blue rounded-lg flex items-center justify-center">
                {student.avatar ? (
                  <img
                    src={student.avatar || "/placeholder.svg"}
                    alt="Student"
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <User className="w-16 h-16 text-white" />
                )}
              </div>
              <div className="flex-1 grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div>
                    <p className="text-gray-300 text-sm">Name</p>
                    <p className="text-white font-medium">{displayStudentInfo.name}</p>
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm">Phone</p>
                    <p className="text-white font-medium">{displayStudentInfo.phone}</p>
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm">Class</p>
                    <p className="text-white font-medium">{displayStudentInfo.class}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <p className="text-gray-300 text-sm">School Name</p>
                    <p className="text-white font-medium">{displayStudentInfo.schoolName}</p>
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm">Email</p>
                    <p className="text-white font-medium">{displayStudentInfo.email}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-300 text-sm">Created On</p>
                      <p className="text-white font-medium">{new Date(displayStudentInfo.createdAt).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-300 text-sm">Subjects</p>
                      <p className="text-white font-medium">{displayStudentInfo.subjects.join(', ') || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Analytics Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Analytics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Quiz Stats */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Quizzes Attempted:</span>
                  <span className="text-sm text-gray-900">
                    {analytics?.quizzesAttempted || 0} (Maths 8, Physics 5, Chemistry 5, Biology 5)
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Quiz Accuracy:</span>
                  <span className="text-sm text-gray-900">{analytics?.quizAccuracy || 0}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Total Time Spent Learning:</span>
                  <span className="text-sm text-gray-900">{analytics?.totalTimeSpent || 'N/A'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Current Learning Streak (days):</span>
                  <span className="text-sm text-gray-900">{analytics?.currentLearningStreak || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Motivation Level Tag:</span>
                  <span className="text-sm text-gray-900">{analytics?.motivationLevel || 'N/A'}</span>
                </div>
              </div>

              {/* Leaderboard Stats */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-500">Leaderboard Insights</h4>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Current Rank:</span>
                  <span className="text-sm text-gray-900">{analytics?.currentRank || 'N/A'}st</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Rank Movement:</span>
                  <span className="text-sm text-gray-900">{analytics?.rankMovement || 'N/A'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Flames Earned (Weekly):</span>
                  <span className="text-sm text-gray-900">{analytics?.flamesEarned || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Topics Completed:</span>
                  <span className="text-sm text-gray-900">{analytics?.topicsCompleted || 0}</span>
                </div>
              </div>

              {/* Additional Stats */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Average Score per Subject</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Last Quiz Taken (date + score)</span>
                  <span className="text-sm text-gray-900">{analytics?.lastQuizDate || 'N/A'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Time of Day Most Active</span>
                  <span className="text-sm text-gray-900">{analytics?.timeOfDayMostActive || 'N/A'}</span>
                </div>
                <h4 className="text-sm font-medium text-gray-500 pt-2">Chat Usage Overview</h4>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Total Messages Sent:</span>
                  <span className="text-sm text-gray-900">{analytics?.chatStats?.totalMessagesSent || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Total Doubts Asked:</span>
                  <span className="text-sm text-gray-900">{analytics?.chatStats?.totalDoubtsAsked || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Most Discussed Subject:</span>
                  <span className="text-sm text-gray-900">{analytics?.chatStats?.mostDiscussedSubject || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quizzes Completed Chart */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="bg-primary-dark text-white p-3 rounded-t-lg -m-6 mb-4">
                <h4 className="font-medium">Quizzes Completed (All Time)</h4>
              </div>
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="w-48 h-48 mx-auto mb-4 relative">
                    {/* Pie Chart Placeholder */}
                    <div className="w-full h-full rounded-full bg-gradient-to-r from-orange-400 via-orange-500 to-gray-400 relative">
                      <div className="absolute inset-4 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex items-center justify-center space-x-4 text-xs">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-blue-500 rounded"></div>
                      <span>Maths</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-orange-500 rounded"></div>
                      <span>Chemistry</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-gray-400 rounded"></div>
                      <span>Physics</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Overall Growth Chart */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="bg-primary-dark text-white p-3 rounded-t-lg -m-6 mb-4">
                <h4 className="font-medium">Overall Growth (All Time)</h4>
              </div>
              <div className="flex items-center justify-center h-64">
                <div className="w-full h-48 bg-gray-50 rounded border relative">
                  {/* Line Chart Placeholder */}
                  <div className="absolute inset-4">
                    <svg className="w-full h-full" viewBox="0 0 300 150">
                      <polyline
                        fill="none"
                        stroke="#ef4444"
                        strokeWidth="2"
                        points="0,140 30,120 60,100 90,85 120,70 150,60 180,45 210,35 240,25 270,15 300,10"
                      />
                      {/* Grid lines */}
                      <defs>
                        <pattern id="grid" width="30" height="15" patternUnits="userSpaceOnUse">
                          <path d="M 30 0 L 0 0 0 15" fill="none" stroke="#e5e7eb" strokeWidth="1" />
                        </pattern>
                      </defs>
                      <rect width="100%" height="100%" fill="url(#grid)" />
                    </svg>
                  </div>
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs text-gray-600">
                    Lesson growth rate
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Issues Raised Section */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Issues Raised</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-primary-dark">
                    <th className="px-6 py-3 text-left text-sm font-medium text-white">User Mail</th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-white">Date</th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-white">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="bg-row-light">
                    <td className="px-6 py-4 text-sm text-gray-900">{student.email}</td>
                    <td className="px-6 py-4 text-sm text-gray-900">1 July 2025 12:23</td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button className="px-3 py-1 bg-primary-dark hover:bg-primary-dark/90 text-white text-xs font-medium rounded transition-colors">
                          View Issue
                        </button>
                        <button className="p-2 rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                          <Download className="w-4 h-4" />
                        </button>
                        <button className="p-2 rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                          <MessageCircle className="w-4 h-4" />
                        </button>
                        <button className="p-2 rounded bg-green-100 hover:bg-green-200 text-green-700 transition-colors">
                          <Award className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Analytics Charts */}
          {chartData && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Activity Charts</h3>
              <AnalyticsCharts
                dailyActivity={chartData.dailyActivity}
                subjectDistribution={chartData.subjectDistribution}
                performanceTrend={chartData.performanceTrend}
              />
            </div>
          )}
          </>
          )}
        </div>
      </div>
    </div>
  )
}
