"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCleanupService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const chatHistory_schema_1 = require("../schemas/chatHistory.schema");
const schedule_1 = require("@nestjs/schedule");
const date_formatter_1 = require("../utils/date_formatter");
let ChatCleanupService = class ChatCleanupService {
    constructor(chatHistoryModel) {
        this.chatHistoryModel = chatHistoryModel;
    }
    async cleanupOldChatHistory() {
        try {
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            const formattedDate = (0, date_formatter_1.formatDate)((0, date_formatter_1.formatTimestampToIOS)(String(sevenDaysAgo)));
            const result = await this.chatHistoryModel.deleteMany({
                date: { $lt: formattedDate }
            });
            console.log(`Deleted ${result.deletedCount} old chat history documents`);
        }
        catch (error) {
            console.error('Error cleaning up chat history:', error);
        }
    }
};
exports.ChatCleanupService = ChatCleanupService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatCleanupService.prototype, "cleanupOldChatHistory", null);
exports.ChatCleanupService = ChatCleanupService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(chatHistory_schema_1.ChatHistory.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ChatCleanupService);
//# sourceMappingURL=chat-cleanup.service.js.map