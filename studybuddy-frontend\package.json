{"name": "studybuddy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@matejmazur/react-katex": "^3.1.3", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.2.7", "@shadcn/ui": "^0.0.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "katex": "^0.16.21", "lucide-react": "^0.468.0", "next": "15.1.0", "react": "^19.0.0", "react-datepicker": "^7.6.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "shadcn-ui": "^0.9.4", "tailwindcss": "^3.4.1", "typescript": "^5"}}