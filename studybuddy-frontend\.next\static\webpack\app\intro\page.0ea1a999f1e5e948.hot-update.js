"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/intro/page",{

/***/ "(app-pages-browser)/./src/app/intro/page.tsx":
/*!********************************!*\
  !*** ./src/app/intro/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OnboardingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_onboarding_login__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/onboarding/login */ \"(app-pages-browser)/./src/components/onboarding/login.tsx\");\n/* harmony import */ var _components_onboarding_step_1__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/onboarding/step-1 */ \"(app-pages-browser)/./src/components/onboarding/step-1.tsx\");\n/* harmony import */ var _components_onboarding_profile_setup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/onboarding/profile-setup */ \"(app-pages-browser)/./src/components/onboarding/profile-setup.tsx\");\n/* harmony import */ var _components_onboarding_step_2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/step-2 */ \"(app-pages-browser)/./src/components/onboarding/step-2.tsx\");\n/* harmony import */ var _components_onboarding_step_3__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/step-3 */ \"(app-pages-browser)/./src/components/onboarding/step-3.tsx\");\n/* harmony import */ var _components_onboarding_step_4__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/step-4 */ \"(app-pages-browser)/./src/components/onboarding/step-4.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction OnboardingPage() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // Start with login (step 0)\n    ;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const nextStep = ()=>{\n        if (currentStep < 5) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const skipIntro = ()=>{\n        // For existing users, redirect directly to dashboard\n        router.push('/dashboard');\n    };\n    const skipToSubjectSelection = ()=>{\n        setCurrentStep(5) // Skip to final step (subject selection) for new users\n        ;\n    };\n    const renderStep = ()=>{\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_login__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onNext: nextStep,\n                    onSkip: skipIntro\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, this);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_1__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onNext: nextStep,\n                    onSkip: skipToSubjectSelection\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, this);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profile_setup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    onNext: nextStep,\n                    onPrev: prevStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_2__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onNext: nextStep,\n                    onPrev: prevStep,\n                    onSkip: skipToSubjectSelection\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_3__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    onNext: nextStep,\n                    onPrev: prevStep,\n                    onSkip: skipToSubjectSelection\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 16\n                }, this);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_4__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    onPrev: prevStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_login__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onNext: nextStep,\n                    onSkip: skipIntro\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: renderStep()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 10\n    }, this);\n}\n_s(OnboardingPage, \"a3DYip2xnuPcyBdHSGEpDO/8Z6w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/intro/page.tsx\n"));

/***/ })

});