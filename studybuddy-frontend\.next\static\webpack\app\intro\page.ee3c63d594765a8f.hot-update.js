"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/intro/page",{

/***/ "(app-pages-browser)/./src/components/onboarding/login.tsx":
/*!*********************************************!*\
  !*** ./src/components/onboarding/login.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LoginPage(param) {\n    let { onNext, onSkip } = param;\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        if (!email.trim() || !password.trim()) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter both email and password\"\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            console.log(\"Attempting login with:\", {\n                email,\n                password: \"***\"\n            });\n            const response = await fetch(\"\".concat(\"http://localhost:3000\" || 0, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            console.log(\"Response ok:\", response.ok);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"Login failed with response:\", errorText);\n                let errorMessage = \"Login failed\";\n                try {\n                    const errorData = JSON.parse(errorText);\n                    errorMessage = errorData.message || errorMessage;\n                } catch (e) {\n                // If response is not JSON, use default message\n                }\n                throw new Error(errorMessage);\n            }\n            const data = await response.json();\n            console.log(\"Login response data:\", data);\n            localStorage.setItem(\"accessToken\", data.accessToken);\n            console.log(\"Token stored in localStorage\");\n            toast({\n                title: \"Success\",\n                description: \"Logged in successfully!\"\n            });\n            // Decide navigation based on profile presence\n            if (data.isUserDetailsPresent) {\n                console.log(\"User has profile details, skipping to dashboard\");\n                onSkip();\n            } else {\n                console.log(\"User needs to complete profile, proceeding to onboarding\");\n                onNext();\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Invalid credentials. Please check your email and password.\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSignUp = ()=>{\n        toast({\n            title: \"Sign Up\",\n            description: \"Please contact your administrator to create a new account.\"\n        });\n    };\n    const handleGetHelp = ()=>{\n        toast({\n            title: \"Help\",\n            description: \"For login assistance, please contact your administrator or IT support.\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col items-center justify-center px-4 py-8 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-3xl md:text-4xl font-bold\",\n                        children: [\n                            \"study\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-blue-500\",\n                                children: \"buddy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-6 md:p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-gray-800 mb-2\",\n                                    children: \"Welcome Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm md:text-base\",\n                                    children: \"Let's get back to Learning!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLogin,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"<EMAIL>\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm md:text-base\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"password\",\n                                        placeholder: \"enter your password\",\n                                        value: password,\n                                        onChange: (e)=>setPassword(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm md:text-base\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: isLoading || !email.trim() || !password.trim(),\n                                    className: \"w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white py-3 rounded-lg font-medium text-sm md:text-base transition-colors\",\n                                    children: isLoading ? \"Logging In...\" : \"Log In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: [\n                                        \"Don't have an account?\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignUp,\n                                            className: \"text-blue-500 hover:text-blue-600 font-medium hover:underline\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGetHelp,\n                                    className: \"text-gray-500 hover:text-gray-700 text-sm hover:underline\",\n                                    children: \"Get Help\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-blue-50 rounded-lg text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-blue-600\",\n                        children: \"Demo: Use any user ID and password to continue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\onboarding\\\\login.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"z+QHlOoit6/DNxc4+4S5LwaqyZI=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/onboarding/login.tsx\n"));

/***/ })

});