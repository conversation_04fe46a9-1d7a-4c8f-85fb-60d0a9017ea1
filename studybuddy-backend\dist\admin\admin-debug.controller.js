"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminDebugController = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const jwt_auth_guard_1 = require("../guard/jwt-auth.guard");
const admin_guard_1 = require("../guard/admin.guard");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../schemas/user.schema");
let AdminDebugController = class AdminDebugController {
    constructor(jwtService, userModel) {
        this.jwtService = jwtService;
        this.userModel = userModel;
    }
    async getTokenInfo(req) {
        const authHeader = req.headers.authorization;
        const token = authHeader?.split(' ')[1];
        try {
            const payload = this.jwtService.verify(token);
            return {
                success: true,
                payload,
                userID: req['userID'],
                hasRole: !!payload.role,
                role: payload.role,
                isAdmin: payload.role === 'admin'
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                token: token ? 'Token present' : 'No token'
            };
        }
    }
    async testAdminAccess() {
        return {
            success: true,
            message: 'Admin access working correctly',
            timestamp: new Date().toISOString()
        };
    }
    async testNoAuth() {
        return {
            success: true,
            message: 'No auth endpoint working',
            timestamp: new Date().toISOString()
        };
    }
    async getUsersInfo() {
        try {
            const users = await this.userModel.find({}, { email: 1, role: 1, _id: 1 }).exec();
            const adminUsers = users.filter(user => user.role === 'admin');
            const studentUsers = users.filter(user => user.role === 'student' || !user.role);
            return {
                success: true,
                totalUsers: users.length,
                adminUsers: adminUsers.length,
                studentUsers: studentUsers.length,
                users: users.map(user => ({
                    id: user._id,
                    email: user.email,
                    role: user.role || 'student'
                }))
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
};
exports.AdminDebugController = AdminDebugController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('token-info'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminDebugController.prototype, "getTokenInfo", null);
__decorate([
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, common_1.Get)('admin-test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminDebugController.prototype, "testAdminAccess", null);
__decorate([
    (0, common_1.Get)('no-auth-test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminDebugController.prototype, "testNoAuth", null);
__decorate([
    (0, common_1.Get)('users-info'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminDebugController.prototype, "getUsersInfo", null);
exports.AdminDebugController = AdminDebugController = __decorate([
    (0, common_1.Controller)('admin-debug'),
    __param(1, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        mongoose_2.Model])
], AdminDebugController);
//# sourceMappingURL=admin-debug.controller.js.map