"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../schemas/user.schema");
const userDetails_schema_1 = require("../schemas/userDetails.schema");
const encrypt_decrypt_1 = require("../utils/encrypt_decrypt");
let UsersService = class UsersService {
    constructor(userModel, userDetailsModel) {
        this.userModel = userModel;
        this.userDetailsModel = userDetailsModel;
    }
    async getAllUsers() {
        return this.userModel.find().exec();
    }
    async updateUser(id, updateData) {
        const encryptPassword = (0, encrypt_decrypt_1.encryptData)(updateData.password);
        updateData.password = encryptPassword.toString();
        const updatedUser = await this.userModel.findByIdAndUpdate(id, updateData, {
            new: true,
        }).exec();
        if (!updatedUser) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return updatedUser;
    }
    async deleteUser(id) {
        const user = this.userModel.findOne({ _id: id }).exec();
        if ((await user).userDetails != null) {
            const userDetailsID = (await user).userDetails._id;
            if (await this.userDetailsModel.findByIdAndDelete(userDetailsID).exec()) {
                if (await this.userModel.findByIdAndDelete(id).exec()) {
                    return { success: true };
                }
                else {
                    throw new common_1.NotFoundException(`User with ID ${id} not found`);
                }
            }
            else {
                throw new common_1.NotFoundException(`User Details with ID ${id} not found`);
            }
        }
        else {
            const result = await this.userModel.findByIdAndDelete(id).exec();
            if (!result) {
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            else {
                return { success: true };
            }
        }
    }
    async findUserByID(id) {
        const userExists = await this.userModel.findById({ _id: id });
        if (!userExists) {
            throw new common_1.NotFoundException(`User with id ${id} not found`);
        }
        return userExists;
    }
    async createUserDetails(userId, createUserDetailsDto) {
        const user = await this.userModel.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found`);
        }
        if (user.userDetails == null) {
            const userDetails = new this.userDetailsModel({ ...createUserDetailsDto, user: userId });
            const savedUserDetails = await userDetails.save();
            user.userDetails = savedUserDetails._id;
            await user.save();
            return savedUserDetails;
        }
        else {
            throw new common_1.NotAcceptableException(`User details already exists with id ${userId} not found`);
        }
    }
    async getUserDetailsByUserId(userId) {
        const user = await this.userModel
            .findById(userId)
            .populate('userDetails')
            .exec();
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found`);
        }
        if (!user.userDetails) {
            throw new common_1.NotFoundException(`UserDetails for user with ID ${userId} not found`);
        }
        return user.userDetails.toJSON();
    }
    async updateUserDetails(userId, updateDto) {
        const user = await this.userModel.findById(userId).exec();
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found`);
        }
        if (!user.userDetails) {
            throw new common_1.NotFoundException(`UserDetails not found for user with ID ${userId}`);
        }
        const userDetails = await this.userDetailsModel
            .findById(user.userDetails)
            .exec();
        if (!userDetails) {
            throw new common_1.NotFoundException(`UserDetails not found for user with ID ${userId}`);
        }
        Object.assign(userDetails, updateDto);
        await userDetails.save();
        return userDetails;
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __param(1, (0, mongoose_1.InjectModel)(userDetails_schema_1.UserDetails.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], UsersService);
//# sourceMappingURL=users.service.js.map