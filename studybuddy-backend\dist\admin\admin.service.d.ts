import { Model } from 'mongoose';
import { Subject } from 'src/schemas/subject.schema';
import { Quiz } from 'src/schemas/quiz.schema';
import { SubjectDto, UpdateSubjectDto, AddTopicDto, UpdateTopicDto } from 'src/dtos/subject.dto';
import { CreateQuizDto, UpdateQuizDto, QuizFilterDto } from 'src/dtos/quiz.dto';
export declare class AdminService {
    private subjectModel;
    private quizModel;
    constructor(subjectModel: Model<Subject>, quizModel: Model<Quiz>);
    getAllSubjects(): Promise<Subject[]>;
    getSubjectById(id: string): Promise<Subject>;
    createSubject(subjectDto: SubjectDto): Promise<Subject>;
    updateSubject(id: string, updateSubjectDto: UpdateSubjectDto): Promise<Subject>;
    deleteSubject(id: string): Promise<{
        success: boolean;
    }>;
    addTopic(addTopicDto: AddTopicDto): Promise<Subject>;
    updateTopic(updateTopicDto: UpdateTopicDto): Promise<Subject>;
    deleteTopic(subjectId: string, topicId: string): Promise<Subject>;
    createQuiz(createQuizDto: CreateQuizDto): Promise<Quiz>;
    getAllQuizzes(filterDto: QuizFilterDto): Promise<Quiz[]>;
    getQuizById(id: string): Promise<Quiz>;
    updateQuiz(id: string, updateQuizDto: UpdateQuizDto): Promise<Quiz>;
    deleteQuiz(id: string): Promise<{
        success: boolean;
    }>;
}
