import { FeedbackStatus } from 'src/schemas/feedback.schema';
export declare class CreateFeedbackDto {
    title: string;
    description: string;
    priority?: string;
    category?: string;
    subject?: string;
    attachments?: string[];
}
export declare class UpdateFeedbackStatusDto {
    status: FeedbackStatus;
    adminResponse?: string;
}
export declare class FeedbackFilterDto {
    status?: FeedbackStatus;
    userId?: string;
}
