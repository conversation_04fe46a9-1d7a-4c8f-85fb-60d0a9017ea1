"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-math";
exports.ids = ["vendor-chunks/remark-math"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-math/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/remark-math/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkMath)\n/* harmony export */ });\n/* harmony import */ var mdast_util_math__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-math */ \"(ssr)/./node_modules/mdast-util-math/lib/index.js\");\n/* harmony import */ var micromark_extension_math__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-extension-math */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js\");\n/// <reference types=\"mdast-util-math\" />\n/// <reference types=\"remark-parse\" />\n/// <reference types=\"remark-stringify\" />\n\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-math').ToOptions} Options\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\n\n\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Add support for math.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction remarkMath(options) {\n  // @ts-expect-error: TS is wrong about `this`.\n  // eslint-disable-next-line unicorn/no-this-assignment\n  const self = /** @type {Processor} */ (this)\n  const settings = options || emptyOptions\n  const data = self.data()\n\n  const micromarkExtensions =\n    data.micromarkExtensions || (data.micromarkExtensions = [])\n  const fromMarkdownExtensions =\n    data.fromMarkdownExtensions || (data.fromMarkdownExtensions = [])\n  const toMarkdownExtensions =\n    data.toMarkdownExtensions || (data.toMarkdownExtensions = [])\n\n  micromarkExtensions.push((0,micromark_extension_math__WEBPACK_IMPORTED_MODULE_0__.math)(settings))\n  fromMarkdownExtensions.push((0,mdast_util_math__WEBPACK_IMPORTED_MODULE_1__.mathFromMarkdown)())\n  toMarkdownExtensions.push((0,mdast_util_math__WEBPACK_IMPORTED_MODULE_1__.mathToMarkdown)(settings))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-math/lib/index.js\n");

/***/ })

};
;