import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class Option extends Document {
    text: string;
    isCorrect: boolean;
}
export declare const OptionSchema: MongooseSchema<Option, import("mongoose").Model<Option, any, any, any, Document<unknown, any, Option> & Option & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Option, Document<unknown, {}, import("mongoose").FlatRecord<Option>> & import("mongoose").FlatRecord<Option> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export declare class Quiz extends Document {
    question: string;
    options: Option[];
    subjectId: MongooseSchema.Types.ObjectId;
    topicId: string;
    type: string;
    difficulty: number;
    explanation?: string;
}
export declare const QuizSchema: MongooseSchema<Quiz, import("mongoose").Model<Quiz, any, any, any, Document<unknown, any, Quiz> & Quiz & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Quiz, Document<unknown, {}, import("mongoose").FlatRecord<Quiz>> & import("mongoose").FlatRecord<Quiz> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
