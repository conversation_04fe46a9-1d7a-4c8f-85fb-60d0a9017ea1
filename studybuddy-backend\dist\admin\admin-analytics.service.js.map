{"version": 3, "file": "admin-analytics.service.js", "sourceRoot": "", "sources": ["../../src/admin/admin-analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,+CAA+C;AAC/C,uCAA2C;AAC3C,wDAA+C;AAC/C,sEAA6D;AAC7D,sEAA6D;AAStD,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YACkC,SAAsB,EACf,gBAAoC,EACpC,gBAAoC;QAF3C,cAAS,GAAT,SAAS,CAAa;QACf,qBAAgB,GAAhB,gBAAgB,CAAoB;QACpC,qBAAgB,GAAhB,gBAAgB,CAAoB;IAC1E,CAAC;IAEJ,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;YAClF,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAC;YACnD,CAAC;YAGD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAkB,CAAC;YAG5C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAGxE,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAGvD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAGrE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAGnF,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;YAGnE,MAAM,SAAS,GAAI,IAAY,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;YAE1E,OAAO;gBACL,WAAW,EAAE;oBACX,MAAM,EAAE,IAAI,CAAC,GAAG;oBAChB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,WAAW,EAAE,IAAI,IAAI,KAAK;oBAChC,KAAK,EAAE,WAAW,EAAE,OAAO,IAAI,KAAK;oBACpC,KAAK,EAAE,WAAW,EAAE,KAAK,IAAI,KAAK;oBAClC,UAAU,EAAE,WAAW,EAAE,UAAU,IAAI,KAAK;oBAC5C,YAAY,EAAE,WAAW,EAAE,YAAY,IAAI,IAAI;oBAC/C,SAAS,EAAE,SAAS;oBACpB,QAAQ,EAAE,WAAW,EAAE,QAAQ,IAAI,EAAE;iBACtC;gBACD,SAAS,EAAE;oBACT,SAAS;oBACT,SAAS;oBACT,gBAAgB;oBAChB,eAAe;iBAChB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,WAAkB;QAC3C,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACpD,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,MAAc,EAAE,OAAY,EAAE,EAAE;gBACrE,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;YACjD,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAG3F,MAAM,aAAa,GAA8B,EAAE,CAAC;QACpD,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACxB,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAe,EAAE,EAAE;gBACxC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACtE,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CACnD,CAAC;QAGF,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,gBAAgB,GAAG,EAAE,CAAC;QACtC,MAAM,cAAc,GAAG,GAAG,KAAK,MAAM,OAAO,KAAK,CAAC;QAGlD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAGjD,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAEjE,OAAO;YACL,aAAa;YACb,WAAW,EAAE,aAAa;YAC1B,oBAAoB;YACpB,cAAc;YACd,mBAAmB;YACnB,MAAM;SACP,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,WAAkB;QAEjE,MAAM,mBAAmB,GAAG;YAC1B,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;YACjD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;YAC5C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;YAC9C,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;SAC7C,CAAC;QAEF,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QAEjG,MAAM,aAAa,GAAG;YACpB,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;YACxC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;YACpC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;YACtC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;SACrC,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QAE/H,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3C,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QAEvF,OAAO;YACL,cAAc;YACd,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC9B,mBAAmB;YACnB,aAAa;YACb,YAAY;YACZ,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,MAAc,EAAE,WAAkB;QACxE,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3F,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACnD,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,MAAc,EAAE,OAAY,EAAE,EAAE;gBACrE,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;YACjD,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAGjD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,CAAC,WAAW,GAAG,GAAG,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CACvE,CAAC;QAGF,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QAE7H,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,WAAW,GAAG,GAAG;YAAE,eAAe,GAAG,MAAM,CAAC;aAC3C,IAAI,WAAW,GAAG,GAAG;YAAE,eAAe,GAAG,QAAQ,CAAC;QAEvD,OAAO;YACL,WAAW;YACX,WAAW;YACX,YAAY,EAAE,GAAG,YAAY,kBAAkB;YAC/C,eAAe;SAChB,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAAC,WAAkB;QACjD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5C,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,GAAW,EAAE,OAAY,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;YAC7G,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;YACvD,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,EAAE;SAC7B,CAAC,CAAC,CAAC;QAGJ,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC;YAChD,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;SAClC,CAAC;QAEF,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC1B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YACtF,IAAI,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,aAAa,CAAC,SAAuC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC;YACxE,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,QAAQ,EAAE,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS;SACtC,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,aAAa;YACb,aAAa;YACb,YAAY;SACb,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,WAAkB;QACxC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAGvC,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAE1G,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAEjG,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;gBACxB,MAAM,EAAE,CAAC;gBACT,WAAW,GAAG,OAAO,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,WAAkB;QAE3C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QACjD,OAAO,GAAG,KAAK,KAAK,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc,EAAE,SAA0B,KAAK;QACzE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAIzD,OAAO;YACL,OAAO,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,wCAAwC;YACxE,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,4BAA4B,MAAM,oBAAoB,MAAM,EAAE;SAC5E,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,MAAc,EAAE,SAAoC,OAAO;QACvF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEzD,OAAO;YACL,aAAa,EAAE,SAAS,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa;YAChE,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC;YACzG,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC;SACpG,CAAC;IACJ,CAAC;IAEO,4BAA4B,CAAC,aAAoB;QACvD,MAAM,aAAa,GAA8B,EAAE,CAAC;QAEpD,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAe,EAAE,EAAE;gBACvC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QAElF,OAAO,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAChE,OAAO;YACP,UAAU,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,OAAO;SACR,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,yBAAyB,CAAC,aAAoB;QACpD,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACnD,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;YACjC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC;SAC9B,CAAC,CAAC,CAAC;IACN,CAAC;CACF,CAAA;AAvRY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,kBAAI,CAAC,IAAI,CAAC,CAAA;IACtB,WAAA,IAAA,sBAAW,EAAC,gCAAW,CAAC,IAAI,CAAC,CAAA;IAC7B,WAAA,IAAA,sBAAW,EAAC,gCAAW,CAAC,IAAI,CAAC,CAAA;qCAFa,gBAAK;QACS,gBAAK;QACL,gBAAK;GAJrD,qBAAqB,CAuRjC"}