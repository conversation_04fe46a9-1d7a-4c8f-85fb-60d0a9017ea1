"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminModule = void 0;
const common_1 = require("@nestjs/common");
const admin_controller_1 = require("./admin.controller");
const admin_service_1 = require("./admin.service");
const admin_debug_controller_1 = require("./admin-debug.controller");
const mongoose_1 = require("@nestjs/mongoose");
const subject_schema_1 = require("../schemas/subject.schema");
const user_schema_1 = require("../schemas/user.schema");
const jwt_1 = require("@nestjs/jwt");
const jwtConfig_1 = require("../config/jwtConfig");
const quiz_schema_1 = require("../schemas/quiz.schema");
let AdminModule = class AdminModule {
};
exports.AdminModule = AdminModule;
exports.AdminModule = AdminModule = __decorate([
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.registerAsync(jwtConfig_1.default.asProvider()),
            mongoose_1.MongooseModule.forFeature([
                { name: subject_schema_1.Subject.name, schema: subject_schema_1.SubjectSchema },
                { name: quiz_schema_1.Quiz.name, schema: quiz_schema_1.QuizSchema },
                { name: user_schema_1.User.name, schema: user_schema_1.UserSchema }
            ])
        ],
        controllers: [admin_controller_1.AdminController, admin_debug_controller_1.AdminDebugController],
        providers: [admin_service_1.AdminService],
        exports: [admin_service_1.AdminService]
    })
], AdminModule);
//# sourceMappingURL=admin.module.js.map