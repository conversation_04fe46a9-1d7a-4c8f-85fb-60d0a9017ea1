import { Model } from 'mongoose';
import { User } from 'src/schemas/user.schema';
import { UserDetails } from 'src/schemas/userDetails.schema';
import { ChatHistory } from 'src/schemas/chatHistory.schema';
export interface LeaderboardUser {
    userId: string;
    name: string;
    studentId: string;
    profileImage?: string;
    sparkPoints: number;
    rank: number;
    class: string;
    subjects: string[];
    totalQueries: number;
    streak: number;
}
export interface LeaderboardResponse {
    users: LeaderboardUser[];
    currentUser?: LeaderboardUser;
    totalUsers: number;
    period: string;
    filters: {
        subject?: string;
        class?: string;
    };
}
export declare class LeaderboardService {
    private userModel;
    private userDetailsModel;
    private chatHistoryModel;
    constructor(userModel: Model<User>, userDetailsModel: Model<UserDetails>, chatHistoryModel: Model<ChatHistory>);
    private getDateRange;
    private formatDate;
    private calculateSparkPoints;
    getLeaderboard(currentUserId: string, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string, limit?: number): Promise<LeaderboardResponse>;
    getUserRank(currentUserId: string, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string): Promise<LeaderboardUser | null>;
    searchUsers(currentUserId: string, searchQuery: string, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string): Promise<LeaderboardResponse>;
    getTopPerformers(currentUserId: string, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string): Promise<{
        topThree: LeaderboardUser[];
        currentUser?: LeaderboardUser;
    }>;
}
