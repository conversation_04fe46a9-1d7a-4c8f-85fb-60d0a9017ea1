"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.decryptData = exports.encryptData = exports.encryptkeyString = void 0;
const crypto_1 = require("crypto");
const alg = 'aes-256-ctr';
exports.encryptkeyString = 'MIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBECoshPnFOL';
const iv = (0, crypto_1.randomBytes)(16);
let key = (0, crypto_1.createHash)('sha256').update(String(exports.encryptkeyString)).digest('base64').substring(0, 32);
const encryptData = (data) => {
    const cipher = (0, crypto_1.createCipheriv)(alg, key, iv);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return `${iv.toString('hex')}:${encrypted}`;
};
exports.encryptData = encryptData;
const decryptData = (encryptedText) => {
    const [iv, encrypted] = encryptedText.split(':');
    const decipher = (0, crypto_1.createDecipheriv)(alg, key, Buffer.from(iv, 'hex'));
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
};
exports.decryptData = decryptData;
//# sourceMappingURL=encrypt_decrypt.js.map