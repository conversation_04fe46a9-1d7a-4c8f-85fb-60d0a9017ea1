import { AdminService } from './admin.service';
import { SubjectDto, UpdateSubjectDto, AddTopicDto, UpdateTopicDto } from 'src/dtos/subject.dto';
import { CreateQuizDto, UpdateQuizDto, QuizFilterDto } from 'src/dtos/quiz.dto';
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getAllSubjects(): Promise<import("../schemas/subject.schema").Subject[]>;
    getSubjectById(id: string): Promise<import("../schemas/subject.schema").Subject>;
    createSubject(subjectDto: SubjectDto): Promise<import("../schemas/subject.schema").Subject>;
    updateSubject(id: string, updateSubjectDto: UpdateSubjectDto): Promise<import("../schemas/subject.schema").Subject>;
    deleteSubject(id: string): Promise<{
        success: boolean;
    }>;
    addTopicForSubject(subjectId: string, topic: {
        name: string;
        description?: string;
    }): Promise<import("../schemas/subject.schema").Subject>;
    addTopic(addTopicDto: AddTopicDto): Promise<import("../schemas/subject.schema").Subject>;
    updateTopic(updateTopicDto: UpdateTopicDto): Promise<import("../schemas/subject.schema").Subject>;
    deleteTopic(subjectId: string, topicId: string): Promise<import("../schemas/subject.schema").Subject>;
    createQuiz(createQuizDto: CreateQuizDto): Promise<import("../schemas/quiz.schema").Quiz>;
    getAllQuizzes(filterDto: QuizFilterDto): Promise<import("../schemas/quiz.schema").Quiz[]>;
    getQuizById(id: string): Promise<import("../schemas/quiz.schema").Quiz>;
    updateQuiz(id: string, updateQuizDto: UpdateQuizDto): Promise<import("../schemas/quiz.schema").Quiz>;
    deleteQuiz(id: string): Promise<{
        success: boolean;
    }>;
}
