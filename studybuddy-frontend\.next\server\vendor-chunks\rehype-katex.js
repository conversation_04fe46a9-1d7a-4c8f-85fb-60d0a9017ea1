"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-katex";
exports.ids = ["vendor-chunks/rehype-katex"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-katex/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-katex/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeKatex)\n/* harmony export */ });\n/* harmony import */ var hast_util_from_html_isomorphic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-from-html-isomorphic */ \"(ssr)/./node_modules/hast-util-from-html-isomorphic/lib/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var katex__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! katex */ \"(ssr)/./node_modules/katex/dist/katex.mjs\");\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {ElementContent, Root} from 'hast'\n * @import {KatexOptions} from 'katex'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<KatexOptions, 'displayMode' | 'throwOnError'>} Options\n */\n\n\n\n\n\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n/** @type {ReadonlyArray<unknown>} */\nconst emptyClasses = []\n\n/**\n * Render elements with a `language-math` (or `math-display`, `math-inline`)\n * class with KaTeX.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeKatex(options) {\n  const settings = options || emptyOptions\n\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree, file) {\n    ;(0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.visitParents)(tree, 'element', function (element, parents) {\n      const classes = Array.isArray(element.properties.className)\n        ? element.properties.className\n        : emptyClasses\n      // This class can be generated from markdown with ` ```math `.\n      const languageMath = classes.includes('language-math')\n      // This class is used by `remark-math` for flow math (block, `$$\\nmath\\n$$`).\n      const mathDisplay = classes.includes('math-display')\n      // This class is used by `remark-math` for text math (inline, `$math$`).\n      const mathInline = classes.includes('math-inline')\n      let displayMode = mathDisplay\n\n      // Any class is fine.\n      if (!languageMath && !mathDisplay && !mathInline) {\n        return\n      }\n\n      let parent = parents[parents.length - 1]\n      let scope = element\n\n      // If this was generated with ` ```math `, replace the `<pre>` and use\n      // display.\n      if (\n        element.tagName === 'code' &&\n        languageMath &&\n        parent &&\n        parent.type === 'element' &&\n        parent.tagName === 'pre'\n      ) {\n        scope = parent\n        parent = parents[parents.length - 2]\n        displayMode = true\n      }\n\n      /* c8 ignore next -- verbose to test. */\n      if (!parent) return\n\n      const value = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__.toText)(scope, {whitespace: 'pre'})\n\n      /** @type {Array<ElementContent> | string | undefined} */\n      let result\n\n      try {\n        result = katex__WEBPACK_IMPORTED_MODULE_0__[\"default\"].renderToString(value, {\n          ...settings,\n          displayMode,\n          throwOnError: true\n        })\n      } catch (error) {\n        const cause = /** @type {Error} */ (error)\n        const ruleId = cause.name.toLowerCase()\n\n        file.message('Could not render math with KaTeX', {\n          ancestors: [...parents, element],\n          cause,\n          place: element.position,\n          ruleId,\n          source: 'rehype-katex'\n        })\n\n        // KaTeX *should* handle `ParseError` itself, but not others.\n        // it doesn’t always:\n        // <https://github.com/remarkjs/react-markdown/issues/853>\n        try {\n          result = katex__WEBPACK_IMPORTED_MODULE_0__[\"default\"].renderToString(value, {\n            ...settings,\n            displayMode,\n            strict: 'ignore',\n            throwOnError: false\n          })\n        } catch {\n          // Generate similar markup if this is an other error.\n          // See: <https://github.com/KaTeX/KaTeX/blob/5dc7af0/docs/error.md>.\n          result = [\n            {\n              type: 'element',\n              tagName: 'span',\n              properties: {\n                className: ['katex-error'],\n                style: 'color:' + (settings.errorColor || '#cc0000'),\n                title: String(error)\n              },\n              children: [{type: 'text', value}]\n            }\n          ]\n        }\n      }\n\n      if (typeof result === 'string') {\n        const root = (0,hast_util_from_html_isomorphic__WEBPACK_IMPORTED_MODULE_3__.fromHtmlIsomorphic)(result, {fragment: true})\n        // Cast as we don’t expect `doctypes` in KaTeX result.\n        result = /** @type {Array<ElementContent>} */ (root.children)\n      }\n\n      const index = parent.children.indexOf(scope)\n      parent.children.splice(index, 1, ...result)\n      return unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.SKIP\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-katex/lib/index.js\n");

/***/ })

};
;