"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/sidebar */ \"(app-pages-browser)/./src/components/admin/sidebar.tsx\");\n/* harmony import */ var _components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/top-nav */ \"(app-pages-browser)/./src/components/admin/top-nav.tsx\");\n/* harmony import */ var _components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/data-table */ \"(app-pages-browser)/./src/components/admin/data-table.tsx\");\n/* harmony import */ var _components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/add-user-modal */ \"(app-pages-browser)/./src/components/admin/add-user-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-copy.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/student-details-modal */ \"(app-pages-browser)/./src/components/admin/student-details-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [selectedUserForDetails, setSelectedUserForDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUserDetailsModalOpen, setIsUserDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const columns = [\n        {\n            key: \"email\",\n            label: \"Mail\"\n        },\n        {\n            key: \"decryptedPassword\",\n            label: \"Password\"\n        }\n    ];\n    const copyUserDetails = (email, password)=>{\n        navigator.clipboard.writeText(\"Email: \".concat(email, \"\\nPassword: \").concat(password));\n        toast({\n            title: \"Copied!\",\n            description: \"User details copied to clipboard\"\n        });\n    };\n    const deleteUser = async (userId)=>{\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/users?id=\").concat(userId), {\n                method: 'DELETE',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) throw new Error('Failed to delete user');\n            setUsers(users.filter((user)=>user._id !== userId));\n            toast({\n                title: 'Success',\n                description: 'User deleted successfully'\n            });\n        } catch (err) {\n            toast({\n                title: 'Error',\n                description: 'Could not delete user'\n            });\n        }\n    };\n    const actions = [\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'View Details',\n            onClick: (row)=>{\n                setSelectedUserForDetails(row);\n                setIsUserDetailsModalOpen(true);\n            },\n            variant: 'view'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: 'Copy',\n            onClick: (row)=>copyUserDetails(row.email, row.decryptedPassword || row.password),\n            variant: 'edit'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            label: 'Delete',\n            onClick: (row)=>deleteUser(row._id),\n            variant: 'delete'\n        }\n    ];\n    const handleAddUser = async (param)=>{\n        let { email, password } = param;\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/auth/register\"), {\n                method: 'POST',\n                headers: getAuthHeaders(),\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) throw new Error('Registration failed');\n            const newUser = await response.json();\n            const decryptedPassword = await decryptPassword(password);\n            setUsers([\n                ...users,\n                {\n                    _id: newUser._id,\n                    email,\n                    password,\n                    decryptedPassword\n                }\n            ]);\n            toast({\n                title: 'Success',\n                description: 'User registered successfully'\n            });\n        } catch (err) {\n            toast({\n                title: 'Error',\n                description: 'Failed to register user'\n            });\n        }\n    };\n    // Token retrieval & user list fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (true) {\n                const stored = localStorage.getItem('accessToken');\n                console.log(\"Admin page - checking token:\", stored ? \"Token found\" : \"No token\");\n                if (!stored) {\n                    console.log(\"Admin page - No token found, redirecting to login\");\n                    router.push('/admin-login');\n                } else {\n                    console.log(\"Admin page - Token found, setting token\");\n                    setToken(stored);\n                }\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        router\n    ]);\n    const getAuthHeaders = ()=>({\n            Authorization: \"Bearer \".concat(token),\n            'Content-Type': 'application/json'\n        });\n    const decryptPassword = async (encrypted)=>{\n        try {\n            const res = await fetch('/api/decrypt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    encryptedPassword: encrypted\n                })\n            });\n            if (!res.ok) throw new Error('decrypt');\n            const data = await res.json();\n            return data.decryptedPassword;\n        } catch (e) {\n            return encrypted;\n        }\n    };\n    const fetchUsers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[fetchUsers]\": async ()=>{\n            try {\n                console.log(\"Admin page - Fetching users with token:\", token ? \"Token present\" : \"No token\");\n                const headers = getAuthHeaders();\n                console.log(\"Admin page - Auth headers:\", headers);\n                const res = await fetch(\"\".concat(\"http://localhost:3000\", \"/users\"), {\n                    headers\n                });\n                console.log(\"Admin page - Users fetch response status:\", res.status);\n                if (!res.ok) {\n                    const errorText = await res.text();\n                    console.error(\"Admin page - Users fetch failed:\", errorText);\n                    throw new Error(\"fetch failed: \".concat(res.status));\n                }\n                const data = await res.json();\n                console.log(\"Admin page - Users data received:\", data.length, \"users\");\n                const list = await Promise.all(data.map({\n                    \"AdminDashboard.useCallback[fetchUsers]\": async (u)=>({\n                            ...u,\n                            decryptedPassword: await decryptPassword(u.password)\n                        })\n                }[\"AdminDashboard.useCallback[fetchUsers]\"]));\n                setUsers(list);\n            } catch (error) {\n                console.error(\"Admin page - Error fetching users:\", error);\n                toast({\n                    title: 'Error',\n                    description: 'Failed to load users'\n                });\n            }\n        }\n    }[\"AdminDashboard.useCallback[fetchUsers]\"], [\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (token) fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        token,\n        fetchUsers\n    ]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const pageSize = 10;\n    const breadcrumbs = [\n        {\n            label: \"Admin Dashboard\",\n            href: \"/dashboard\"\n        },\n        {\n            label: \"Student Details\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: \"Admin Dashboard\",\n                        breadcrumbs: breadcrumbs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            title: \"Students\",\n                            count: users.length,\n                            columns: columns,\n                            data: users,\n                            actions: actions,\n                            onAddNew: ()=>setIsModalOpen(true),\n                            addButtonLabel: \"Add User\",\n                            page: page,\n                            pageSize: pageSize,\n                            onPageChange: setPage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSubmit: handleAddUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isUserDetailsModalOpen,\n                onClose: ()=>setIsUserDetailsModalOpen(false),\n                student: selectedUserForDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"okxVSTvzIsIRsEPCA1KS3dqEfNo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});