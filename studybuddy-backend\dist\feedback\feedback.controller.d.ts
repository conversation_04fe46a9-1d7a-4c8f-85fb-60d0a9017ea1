import { FeedbackService } from './feedback.service';
import { CreateFeedbackDto, UpdateFeedbackStatusDto, FeedbackFilterDto } from 'src/dtos/feedback.dto';
export declare class FeedbackController {
    private readonly feedbackService;
    constructor(feedbackService: FeedbackService);
    createFeedback(req: any, createFeedbackDto: CreateFeedbackDto): Promise<import("../schemas/feedback.schema").Feedback>;
    getUserFeedbacks(req: any): Promise<import("../schemas/feedback.schema").Feedback[]>;
    getUserFeedbackById(req: any, id: string): Promise<import("../schemas/feedback.schema").Feedback>;
    getAllFeedbacks(filterDto: FeedbackFilterDto): Promise<import("../schemas/feedback.schema").Feedback[]>;
    getFeedbackById(id: string): Promise<import("../schemas/feedback.schema").Feedback>;
    updateFeedbackStatus(id: string, updateFeedbackStatusDto: UpdateFeedbackStatusDto): Promise<import("../schemas/feedback.schema").Feedback>;
    deleteFeedback(id: string): Promise<{
        success: boolean;
    }>;
}
