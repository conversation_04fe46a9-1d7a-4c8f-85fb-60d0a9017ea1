"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../guard/jwt-auth.guard");
const leaderboard_service_1 = require("./leaderboard.service");
const swagger_1 = require("@nestjs/swagger");
let LeaderboardController = class LeaderboardController {
    constructor(leaderboardService) {
        this.leaderboardService = leaderboardService;
    }
    async getLeaderboard(req, period = 'all', subject, classFilter, limit = '50') {
        const currentUserId = req['userID'];
        return await this.leaderboardService.getLeaderboard(currentUserId, period, subject, classFilter, parseInt(limit));
    }
    async getUserRank(req, period = 'all', subject, classFilter) {
        const currentUserId = req['userID'];
        return await this.leaderboardService.getUserRank(currentUserId, period, subject, classFilter);
    }
    async searchUsers(req, searchQuery, period = 'all', subject, classFilter) {
        const currentUserId = req['userID'];
        return await this.leaderboardService.searchUsers(currentUserId, searchQuery, period, subject, classFilter);
    }
    async getTopPerformers(req, period = 'all', subject, classFilter) {
        const currentUserId = req['userID'];
        return await this.leaderboardService.getTopPerformers(currentUserId, period, subject, classFilter);
    }
};
exports.LeaderboardController = LeaderboardController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get leaderboard rankings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Leaderboard data retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, enum: ['weekly', 'monthly', 'all'], description: 'Time period for leaderboard' }),
    (0, swagger_1.ApiQuery)({ name: 'subject', required: false, description: 'Filter by specific subject' }),
    (0, swagger_1.ApiQuery)({ name: 'class', required: false, description: 'Filter by class/grade' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Number of users to return (default: 50)' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('period')),
    __param(2, (0, common_1.Query)('subject')),
    __param(3, (0, common_1.Query)('class')),
    __param(4, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String]),
    __metadata("design:returntype", Promise)
], LeaderboardController.prototype, "getLeaderboard", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('user-rank'),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user rank and stats' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User rank data retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, enum: ['weekly', 'monthly', 'all'], description: 'Time period for ranking' }),
    (0, swagger_1.ApiQuery)({ name: 'subject', required: false, description: 'Filter by specific subject' }),
    (0, swagger_1.ApiQuery)({ name: 'class', required: false, description: 'Filter by class/grade' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('period')),
    __param(2, (0, common_1.Query)('subject')),
    __param(3, (0, common_1.Query)('class')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], LeaderboardController.prototype, "getUserRank", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Search users in leaderboard' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Search results retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'query', required: true, description: 'Search query (name or student ID)' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, enum: ['weekly', 'monthly', 'all'], description: 'Time period for leaderboard' }),
    (0, swagger_1.ApiQuery)({ name: 'subject', required: false, description: 'Filter by specific subject' }),
    (0, swagger_1.ApiQuery)({ name: 'class', required: false, description: 'Filter by class/grade' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('query')),
    __param(2, (0, common_1.Query)('period')),
    __param(3, (0, common_1.Query)('subject')),
    __param(4, (0, common_1.Query)('class')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String]),
    __metadata("design:returntype", Promise)
], LeaderboardController.prototype, "searchUsers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('top-performers'),
    (0, swagger_1.ApiOperation)({ summary: 'Get top 3 performers for podium view' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Top performers retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'period', required: false, enum: ['weekly', 'monthly', 'all'], description: 'Time period for ranking' }),
    (0, swagger_1.ApiQuery)({ name: 'subject', required: false, description: 'Filter by specific subject' }),
    (0, swagger_1.ApiQuery)({ name: 'class', required: false, description: 'Filter by class/grade' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('period')),
    __param(2, (0, common_1.Query)('subject')),
    __param(3, (0, common_1.Query)('class')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], LeaderboardController.prototype, "getTopPerformers", null);
exports.LeaderboardController = LeaderboardController = __decorate([
    (0, swagger_1.ApiTags)('Leaderboard'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('leaderboard'),
    __metadata("design:paramtypes", [leaderboard_service_1.LeaderboardService])
], LeaderboardController);
//# sourceMappingURL=leaderboard.controller.js.map