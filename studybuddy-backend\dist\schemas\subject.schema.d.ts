import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class Topic extends Document {
    name: string;
    description: string;
}
export declare const TopicSchema: MongooseSchema<Topic, import("mongoose").Model<Topic, any, any, any, Document<unknown, any, Topic> & Topic & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Topic, Document<unknown, {}, import("mongoose").FlatRecord<Topic>> & import("mongoose").FlatRecord<Topic> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export declare class Subject extends Document {
    name: string;
    description: string;
    topics: Topic[];
}
export declare const SubjectSchema: MongooseSchema<Subject, import("mongoose").Model<Subject, any, any, any, Document<unknown, any, Subject> & Subject & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Subject, Document<unknown, {}, import("mongoose").FlatRecord<Subject>> & import("mongoose").FlatRecord<Subject> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
