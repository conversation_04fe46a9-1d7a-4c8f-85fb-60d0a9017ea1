"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../app.module");
const user_schema_1 = require("../schemas/user.schema");
const mongoose_1 = require("@nestjs/mongoose");
const encrypt_decrypt_1 = require("../utils/encrypt_decrypt");
async function createAdminUser() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const userModel = app.get((0, mongoose_1.getModelToken)(user_schema_1.User.name));
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123';
    try {
        const existingAdmin = await userModel.findOne({ email: adminEmail });
        if (existingAdmin) {
            console.log('Admin user already exists');
            if (existingAdmin.role !== 'admin') {
                existingAdmin.role = 'admin';
                await existingAdmin.save();
                console.log('Updated existing user role to admin');
            }
            console.log('Admin details:');
            console.log('Email:', adminEmail);
            console.log('Password:', adminPassword);
            console.log('Role:', existingAdmin.role);
        }
        else {
            const encryptedPassword = (0, encrypt_decrypt_1.encryptData)(adminPassword);
            const adminUser = new userModel({
                email: adminEmail,
                password: encryptedPassword,
                role: 'admin'
            });
            await adminUser.save();
            console.log('Admin user created successfully!');
            console.log('Email:', adminEmail);
            console.log('Password:', adminPassword);
            console.log('Role: admin');
        }
    }
    catch (error) {
        console.error('Error creating admin user:', error);
    }
    finally {
        await app.close();
    }
}
createAdminUser();
//# sourceMappingURL=create-admin.js.map