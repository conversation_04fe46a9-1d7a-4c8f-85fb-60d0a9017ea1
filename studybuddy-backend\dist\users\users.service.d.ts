import { Model } from 'mongoose';
import { User } from 'src/schemas/user.schema';
import { UserDto } from 'src/dtos/user.dto';
import { CreateUserDetailsDto } from 'src/dtos/createUserDetailsDto';
import { UserDetails } from 'src/schemas/userDetails.schema';
export declare class UsersService {
    private userModel;
    private userDetailsModel;
    constructor(userModel: Model<User>, userDetailsModel: Model<UserDetails>);
    getAllUsers(): Promise<User[]>;
    updateUser(id: string, updateData: UserDto): Promise<User>;
    deleteUser(id: string): Promise<{
        success: boolean;
    }>;
    findUserByID(id: string): Promise<User>;
    createUserDetails(userId: string, createUserDetailsDto: CreateUserDetailsDto): Promise<import("mongoose").Document<unknown, {}, UserDetails> & UserDetails & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
    getUserDetailsByUserId(userId: string): Promise<string>;
    updateUserDetails(userId: string, updateDto: CreateUserDetailsDto): Promise<UserDetails>;
}
