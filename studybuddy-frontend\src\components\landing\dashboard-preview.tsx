import { MessageCircle } from "lucide-react"

export function DashboardPreview() {
  return (
    <section className="relative z-10 px-4 sm:px-6 lg:px-8 pb-16">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-center">
          <div className="relative max-w-4xl w-full">
            {/* Desktop Dashboard Image */}
            <div className="hidden md:block">
              <div className="bg-white rounded-2xl shadow-2xl border-4 border-[#309CEC] p-6 transform rotate-1 hover:rotate-0 transition-transform duration-300">
                <div className="aspect-[16/10] bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-[#309CEC]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MessageCircle className="w-8 h-8 text-[#309CEC]" />
                    </div>
                    <p className="text-gray-600 font-medium">Desktop Dashboard Preview</p>
                    <p className="text-sm text-gray-500 mt-2">Dashboard image will be displayed here</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile Dashboard Image */}
            <div className="block md:hidden">
              <div className="bg-white rounded-2xl shadow-xl border-2 border-[#309CEC] p-4">
                <div className="aspect-[4/3] bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-[#309CEC]/10 rounded-full flex items-center justify-center mx-auto mb-3">
                      <MessageCircle className="w-6 h-6 text-[#309CEC]" />
                    </div>
                    <p className="text-gray-600 font-medium text-sm">Mobile Dashboard Preview</p>
                    <p className="text-xs text-gray-500 mt-1">Mobile dashboard image here</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-yellow-400 rounded-full animate-bounce hidden lg:block" />
            <div className="absolute -top-2 -right-6 w-6 h-6 bg-pink-400 rounded-full animate-pulse hidden lg:block" />
            <div className="absolute -bottom-6 -left-2 w-10 h-10 bg-green-400 rounded-full animate-bounce delay-300 hidden lg:block" />
          </div>
        </div>
      </div>
    </section>
  )
}
