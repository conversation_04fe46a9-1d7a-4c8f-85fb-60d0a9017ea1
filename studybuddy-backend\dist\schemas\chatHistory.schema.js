"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryResponseSchema = exports.ChatHistorySchema = exports.ChatHistory = exports.SubjectWise = exports.QueryResponse = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoose = require("mongoose");
let QueryResponse = class QueryResponse {
};
exports.QueryResponse = QueryResponse;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], QueryResponse.prototype, "query", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], QueryResponse.prototype, "response", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QueryResponse.prototype, "tokensUsed", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], QueryResponse.prototype, "summary", void 0);
exports.QueryResponse = QueryResponse = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], QueryResponse);
let SubjectWise = class SubjectWise {
};
exports.SubjectWise = SubjectWise;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], SubjectWise.prototype, "subject", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [QueryResponse], default: null }),
    __metadata("design:type", Array)
], SubjectWise.prototype, "queries", void 0);
exports.SubjectWise = SubjectWise = __decorate([
    (0, mongoose_1.Schema)()
], SubjectWise);
let ChatHistory = class ChatHistory {
};
exports.ChatHistory = ChatHistory;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], ChatHistory.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ChatHistory.prototype, "date", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [SubjectWise], default: null }),
    __metadata("design:type", Array)
], ChatHistory.prototype, "subjectWise", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, default: 0 }),
    __metadata("design:type", Number)
], ChatHistory.prototype, "totalTokensSpent", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], required: true, default: null }),
    __metadata("design:type", Array)
], ChatHistory.prototype, "subjects", void 0);
exports.ChatHistory = ChatHistory = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], ChatHistory);
const QueryResponseSchema = mongoose_1.SchemaFactory.createForClass(QueryResponse);
exports.QueryResponseSchema = QueryResponseSchema;
const ChatHistorySchema = mongoose_1.SchemaFactory.createForClass(ChatHistory);
exports.ChatHistorySchema = ChatHistorySchema;
ChatHistorySchema.index({ userId: 1, date: 1 }, { unique: true });
//# sourceMappingURL=chatHistory.schema.js.map