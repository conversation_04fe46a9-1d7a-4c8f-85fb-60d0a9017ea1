import { Document, Schema as MongooseSchema } from 'mongoose';
export declare enum FeedbackStatus {
    PENDING = "pending",
    COMPLETED = "completed",
    REJECTED = "rejected",
    OPEN = "open",
    IN_PROGRESS = "in-progress",
    RESOLVED = "resolved",
    CLOSED = "closed"
}
export declare class Feedback extends Document {
    title: string;
    description: string;
    userId: MongooseSchema.Types.ObjectId;
    userName: string;
    status: FeedbackStatus;
    adminResponse: string;
    priority: string;
    category: string;
    subject: string;
    attachments: string[];
}
export declare const FeedbackSchema: MongooseSchema<Feedback, import("mongoose").Model<Feedback, any, any, any, Document<unknown, any, Feedback> & Feedback & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Feedback, Document<unknown, {}, import("mongoose").FlatRecord<Feedback>> & import("mongoose").FlatRecord<Feedback> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
