"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../schemas/user.schema");
const userDetails_schema_1 = require("../schemas/userDetails.schema");
const chatHistory_schema_1 = require("../schemas/chatHistory.schema");
let LeaderboardService = class LeaderboardService {
    constructor(userModel, userDetailsModel, chatHistoryModel) {
        this.userModel = userModel;
        this.userDetailsModel = userDetailsModel;
        this.chatHistoryModel = chatHistoryModel;
    }
    getDateRange(period) {
        const now = new Date();
        const endDate = this.formatDate(now);
        switch (period) {
            case 'weekly':
                const weekStart = new Date(now);
                weekStart.setDate(now.getDate() - 7);
                return { startDate: this.formatDate(weekStart), endDate };
            case 'monthly':
                const monthStart = new Date(now);
                monthStart.setMonth(now.getMonth() - 1);
                return { startDate: this.formatDate(monthStart), endDate };
            case 'all':
            default:
                return {};
        }
    }
    formatDate(date) {
        return date.toISOString().split('T')[0];
    }
    calculateSparkPoints(totalTokens, totalQueries, streak) {
        const basePoints = Math.floor(totalTokens / 100);
        const queryBonus = totalQueries * 5;
        const streakMultiplier = Math.min(1 + (streak * 0.1), 2);
        return Math.floor((basePoints + queryBonus) * streakMultiplier);
    }
    async getLeaderboard(currentUserId, period = 'all', subject, classFilter, limit = 50) {
        const { startDate, endDate } = this.getDateRange(period);
        const matchStage = {};
        if (startDate && endDate) {
            matchStage.date = { $gte: startDate, $lte: endDate };
        }
        if (subject) {
            matchStage.subjects = subject;
        }
        const userStats = await this.chatHistoryModel.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: '$userId',
                    totalTokens: { $sum: '$totalTokensSpent' },
                    totalQueries: { $sum: { $size: { $ifNull: ['$subjectWise', []] } } },
                    uniqueSubjects: { $addToSet: '$subjects' },
                    streak: { $sum: 1 }
                }
            },
            {
                $project: {
                    userId: '$_id',
                    totalTokens: 1,
                    totalQueries: 1,
                    subjects: { $reduce: { input: '$uniqueSubjects', initialValue: [], in: { $setUnion: ['$$value', '$$this'] } } },
                    streak: 1,
                    sparkPoints: {
                        $floor: {
                            $multiply: [
                                { $add: [{ $divide: ['$totalTokens', 100] }, { $multiply: ['$totalQueries', 5] }] },
                                { $min: [{ $add: [1, { $multiply: ['$streak', 0.1] }] }, 2] }
                            ]
                        }
                    }
                }
            },
            { $sort: { sparkPoints: -1 } }
        ]);
        const userIds = userStats.map(stat => stat.userId);
        const users = await this.userModel.find({ _id: { $in: userIds } })
            .populate('userDetails')
            .exec();
        let leaderboardUsers = [];
        for (const userStat of userStats) {
            const user = users.find(u => u._id.toString() === userStat.userId.toString());
            if (!user || !user.userDetails)
                continue;
            const userDetails = user.userDetails;
            if (classFilter && userDetails.class !== classFilter)
                continue;
            leaderboardUsers.push({
                userId: user._id.toString(),
                name: userDetails.name,
                studentId: userDetails.phoneno,
                profileImage: userDetails.profileImage,
                sparkPoints: userStat.sparkPoints,
                rank: 0,
                class: userDetails.class,
                subjects: userStat.subjects,
                totalQueries: userStat.totalQueries,
                streak: userStat.streak
            });
        }
        leaderboardUsers.sort((a, b) => b.sparkPoints - a.sparkPoints);
        leaderboardUsers.forEach((user, index) => {
            user.rank = index + 1;
        });
        const limitedUsers = leaderboardUsers.slice(0, limit);
        const currentUser = leaderboardUsers.find(user => user.userId === currentUserId);
        return {
            users: limitedUsers,
            currentUser,
            totalUsers: leaderboardUsers.length,
            period,
            filters: { subject, class: classFilter }
        };
    }
    async getUserRank(currentUserId, period = 'all', subject, classFilter) {
        const leaderboard = await this.getLeaderboard(currentUserId, period, subject, classFilter, 1000);
        return leaderboard.currentUser || null;
    }
    async searchUsers(currentUserId, searchQuery, period = 'all', subject, classFilter) {
        const leaderboard = await this.getLeaderboard(currentUserId, period, subject, classFilter, 1000);
        const filteredUsers = leaderboard.users.filter(user => user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.studentId.includes(searchQuery));
        return {
            ...leaderboard,
            users: filteredUsers.slice(0, 50),
            totalUsers: filteredUsers.length
        };
    }
    async getTopPerformers(currentUserId, period = 'all', subject, classFilter) {
        const leaderboard = await this.getLeaderboard(currentUserId, period, subject, classFilter, 3);
        return {
            topThree: leaderboard.users.slice(0, 3),
            currentUser: leaderboard.currentUser
        };
    }
};
exports.LeaderboardService = LeaderboardService;
exports.LeaderboardService = LeaderboardService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __param(1, (0, mongoose_1.InjectModel)(userDetails_schema_1.UserDetails.name)),
    __param(2, (0, mongoose_1.InjectModel)(chatHistory_schema_1.ChatHistory.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model])
], LeaderboardService);
//# sourceMappingURL=leaderboard.service.js.map